import asyncio
import sys
import os
import logging
logging.basicConfig(level=logging.INFO)
sys.path.insert(0, os.getcwd())
from extractor.bilibili.yt_dlp import main
from models import ExtractReq

async def test():
    req = ExtractReq(url='https://space.bilibili.com/10119428', extractor='bilibili')
    try:
        result = await main(req)
        print(f'成功提取，用户: {result.user.username if result.user else None}')
        print(f'视频数量: {len(result.posts)}')
        for i, post in enumerate(result.posts[:3]):
            print(f'{i+1}. {post.text[:60]}... (ID: {post.id})')
    except Exception as e:
        print(f'提取失败: {e}')

asyncio.run(test())
