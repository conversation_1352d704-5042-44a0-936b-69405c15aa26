## New FastAPI Project

pip install fastapi fastapi-cli 'uvicorn[standard]'

#### 依赖

- "httpx[http2]"
- "yt-dlp[default,curl-cffi]"
- "sqlalchemy[asyncio]"
- pydantic-settings
- redis
- types-redis

## Vendor 目录文件来源

- mime.types 来自 macOS /etc/apache2/mime.types

### 升级版本注意

1. 升级 Python 版本不要太激进，有些依赖包可能不支持比较新的 Python 版本。升级到维护状态为 security 的最新版本即可。https://www.python.org/downloads/
2. 本工程目前使用 Python v3.10.14
