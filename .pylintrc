[MESSAGES CONTROL]
disable=C0116,  # Missing function or method docstring
        C0115,  # Missing class docstring 
        C0114,  # Missing module docstring  
        C0303,  # Trailing whitespace,
        C0301,  # Line too long
        W0702,  # No exception type(s) specified
        E0401,  # Unable to import
        E0611,  # No name in module,
        W0406,  # Module import itself
        W1202,  # Use % formatting in logging functions and pass the % parameters as arguments
        W1203,  # Use % formatting in logging functions and pass the % parameters as arguments
        W0718,  # Catching too general exception Exception
        W0707,  # Consider explicitly re-raising using the 'from' keyword
        R0903,  # Too few public methods
        R1710,  # Either all return statements in a function should return an expression, or none of them should.
        R0902,  # Too many instance attributes

[FORMAT]
max-line-length=500