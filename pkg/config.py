from typing import Any
from pydantic_settings import BaseSettings, SettingsConfigDict


class _Settings(BaseSettings):
    """Environment variables"""

    node_name: str  # node name
    redis_url: str  # redis url
    aes_key: str = "KYt0sY3mN1OQ0TS6"
    feishu_alarm_api: str = "https://open.feishu.cn/open-apis/bot/v2/hook/1078b78a-c0c2-4001-b690-20f73466c669"
    ipv6_prefix: str | None = None  # ipv6 prefix

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


settings = _Settings()


LOGGING_CONFIG: dict[str, Any] = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(levelname)s %(asctime)s %(module)s %(threadName)s %(message)s",
        },
    },
    "filters": {
        "critical_alarm_filter": {
            "()": "pkg.logging.CriticalAlarmFilter",
        },
    },
    "handlers": {
        "console": {
            "filters": ["critical_alarm_filter"],
            "formatter": "json",
            "class": "logging.StreamHandler",
        },
    },
    "loggers": {
        "extractor": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "pkg": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "router": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "main": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "WARNING",
        "propagate": False,
    },
}
