import mimetypes

from knifes import urls
from knifes.media import MediaType



# https://youtu.be/8q2hcCE7raU
# https://www.youtube.com/watch?v=TZoTJSDGU2c
def extract_youtube_video_id(url):
    hostname, path_list, query_dict = urls.parse_url(url)
    vid = None
    if "youtu.be" in hostname:
        vid = path_list[0]
    elif path_list[0] == "watch":
        vid = query_dict.get("v")
    elif path_list[0] in ("shorts", "live"):
        vid = path_list[1]

    if not vid or not vid.strip():
        raise ValueError("unable extract youtube video id from the given url")
    return vid.strip()


def get_youtube_thumbnail_url(vid):
    return f"https://i.ytimg.com/vi/{vid}/maxresdefault.jpg"


def guess_media_type(ext) -> MediaType:
    if not ext:
        return MediaType.VIDEO
    mimetype, _ = mimetypes.guess_type(f"test.{ext}")
    if not mimetype:
        return MediaType.VIDEO
    try:
        return MediaType(mimetype.split("/")[0])
    except:  # noqa
        return MediaType.VIDEO
