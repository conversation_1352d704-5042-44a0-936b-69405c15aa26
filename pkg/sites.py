from enum import Enum
from knifes import urls
from pydantic import BaseModel, Field, computed_field


class AreaEnum(Enum):
    DOMESTIC = 1
    OVERSEAS = 2

    def __str__(self):
        return self.name.lower()


class Node(BaseModel):
    base_url: str = Field(exclude=True)  # 节点默认访问地址
    internal_url: str | None = Field(default=None, exclude=True)  # 内部优先访问的节点地址

    @computed_field
    def internal_base_url(self) -> str:
        return self.internal_url or self.base_url

    @computed_field
    def external_base_url(self) -> str:
        return self.base_url


NODE_DICT = {
    "gz": Node(base_url="http://**************:30082", internal_url="http://*********:30082"),
    "gz-go": Node(base_url="http://**************:30080", internal_url="http://*********:30080"),
    "hk": Node(base_url="http://**************:30082", internal_url="http://*********:30082"),
    "hk-go": Node(base_url="http://**************:30080", internal_url="http://*********:30080"),
    "us-east": Node(base_url="http://***********:30082", internal_url="http://*********:30082"),

    "las-vegas": Node(base_url="https://garden-us-cdn.aipark.top"),
    "las-vegas-go": Node(base_url="https://garden-go-us-cdn.aipark.top"),
    "local": Node(base_url="http://127.0.0.1:8000")
}


OVERSEAS_MAIN_NODE = NODE_DICT["us-east"]  # 海外主节点  短地址换长地址需要
DOMESTIC_MAIN_NODE = NODE_DICT["gz"]  # 国内主节点


class SiteEnum(Enum):
    WXMP = ("微信公众号", "微信公众号视频", False)
    DOUYIN = ("抖音", "抖音(视频、图集)", False)
    DOUYIN_FREE = ("douyin", "douyin(视频、图集)", False)
    TIKTOK = ("TikTok", "TikTok视频", True)
    LEMON8 = ("Lemon8", "Lemon8(视频、图集)", True)
    TOUTIAO = (
        "今日头条、西瓜视频、阳光宽频",
        "今日头条、西瓜视频、阳光宽频视频",
        False,
    )
    PIPIXIA = ("皮皮虾", "皮皮虾视频", False)
    KUAISHOU = ("KS", "KS", False)
    KWAI = ("KW", "KW", True)
    WEIBO = (
        "微博、秒拍、绿洲、小咖秀、晃咖",
        "微博、秒拍、绿洲、小咖秀、晃咖(视频、图片)",
        False,
    )
    XIAOHONGSHU = ("小红书", "小红书(视频、图集)", False)
    XIAOHONGSHU_API = ("小红书API", "小红书(视频、图集)", False)
    BILIBILI = ("哔哩哔哩", "哔哩哔哩视频", False)
    BILIBILI_FREE = ("bilibili", "bilibili视频", False)
    ACFUN = ("AcFun", "AcFun视频", False)
    MEIPAI = ("美拍", "美拍视频、WIDE短视频", False)
    MEITU = ("美图秀秀", "美图秀秀(视频、图集)", False)
    QUANMIN = ("全民小视频、百度好看视频", "全民小视频、百度好看视频", False)
    XIAOYING = ("小影", "小影视频", False)
    MOMO = ("陌陌", "陌陌视频", False)
    YUNYINYUE = ("网易云音乐", "网易云音乐视频", False)
    WEISHI = ("微视", "微视短视频", False)
    KG = ("全民K歌", "全民K歌(视频、音频)", False)
    SUNO = ("Suno", "SunoAI音乐", True)
    ZUIYOU = ("最右", "最右视频", False)
    THREADS = ("Threads", "Threads(视频、图片)", True)
    INSTAGRAM = ("Instagram", "Instagram(视频、图片)", True)
    INSTAGRAM_FREE = ("Ins", "Ins(视频、图片)", True)
    INSTAGRAM_PRO = ("ins", "ins(视频、图片)", True)
    YOUTUBE = ("YouTube", "YouTube视频", True)
    YOUTUBE_FREE = ("油管", "油管视频", True)
    VIMEO = ("Vimeo", "Vimeo视频", True)
    VK = ("VK", "VK视频", True)
    DAILYMOTION = ("Dailymotion", "Dailymotion视频", True)
    FACEBOOK = ("Facebook", "Facebook视频", True)
    # FACEBOOK_FREE = ("fb", "fb视频", True)
    TWITTER = ("Twitter", "Twitter视频", True)
    TWITTER_FREE = ("推特", "推特视频", True)
    PEARVIDEO = ("梨视频", "梨视频", False)
    ULIKECAM = ("剪映", "剪映视频", False)
    UC = ("UC", "UC视频", False)
    KANDIAN = ("QQ看点", "QQ看点视频", False)
    PIPIGX = ("皮皮搞笑", "皮皮搞笑视频", False)
    YINYUETAI = ("音悦台", "音悦台", False)
    TUMBLR = ("Tumblr", "Tumblr视频", True)
    REDDIT = ("Reddit", "Reddit视频", True)
    PORNHUB = ("Pornhub", "Pornhub视频", True)
    XVIDEOS = ("Xvideos", "Xvideos视频", True)
    LIKEE = ("Likee", "Likee视频", True)
    TRILLER = ("Triller", "Triller视频", True)
    PINTEREST = ("Pinterest", "Pinterest(视频、图片)", True)

    TENCENT = ("腾讯视频", "腾讯视频", False)
    IQIYI = ("爱奇艺", "爱奇艺视频", False)
    SOHU = ("搜狐", "搜狐视频", False)
    CCTV = ("央视网", "CCTV视频", False)

    # TAOBAO = ("淘宝、天猫、1688", "淘宝、天猫、1688视频", False)
    # JD = ("京东", "京东(视频、图集)", False)
    # PDD = ("拼多多", "拼多多(视频、图集)", False)
    # YOUKU = ("优酷", "优酷视频", False)

    YT_DLP_OVERSEAS = ("海外1", "Twitch、afreecatv", True)
    YT_DLP_DOMESTIC = ("国外1", "", False)
    YOU_GET_OVERSEAS = ("海外2", "", True)
    YOU_GET_DOMESTIC = ("国外2", "", False)

    YJJX = (
        "yjjx",
        "淘宝、天猫、1688、得物(视频、图集)、刷宝、迅雷、开眼视频、糗事百科视频",
        False,
    )  # yijianjiexi
    # VNIL = ("vnil", "", False)  # vnil
    FLVCD = ("flvcd", "虎牙、斗鱼、糖豆、酷燃、新华网、虎嗅", False)  # flvcd

    BATCH_TWITTER = ("Twitter主页批量", "Twitter", True)
    BATCH_YOUTUBE = ("YouTube主页批量", "YouTube(频道页、播放列表)", True)
    BATCH_INSTAGRAM = ("Instagram主页批量", "Instagram(作者主页、Hashtag列表)", True)
    BATCH_TIKTOK = ("TikTok主页批量", "TikTok(作者主页、Hashtag列表)", True)
    BATCH_DOUYIN = ("抖音主页批量", "抖音(主页、合集)", False)
    BATCH_KUAISHOU = ("快手主页批量", "快手(主页、合集)", False)
    BATCH_WEIBO = ("微博主页批量", "微博", False)
    BATCH_YJJX = ("yjjx", "主页批量", False)
    # BATCH_VNIL = ("vnil", "主页批量", False)
    BATCH_BILIBILI = ("bilibili主页批量", "哔哩哔哩(主页、合集)", False)

    def __init__(self, desc, full_desc, overseas):
        self.desc = desc
        self.full_desc = full_desc
        self.overseas = overseas

    @classmethod
    def sites(cls):
        return [member.site for member in cls.__members__.values()]

    @property
    def site(self):
        return self.name.lower()

    @classmethod
    def get_by_site(cls, site: str):
        if not site:
            return None
        return next(filter(lambda x: x.site == site, cls.__members__.values()), None)


# https://s.kw.ai/p/xBdTt9Jj
# t.cn
# t.co
# t.me 不属于短地址
SHORT_DOMAIN_DICT = {
    "url": AreaEnum.DOMESTIC,
    "t.cn": AreaEnum.DOMESTIC,
    "dwz.cn": AreaEnum.DOMESTIC,  # 百度短网址
    "suo": AreaEnum.DOMESTIC,
    "b23.tv": AreaEnum.DOMESTIC,
    "dw4.co": AreaEnum.DOMESTIC,  # 得物
    "xima.tv": AreaEnum.DOMESTIC,  # 喜马拉雅
    "t.co": AreaEnum.OVERSEAS,
    "kw.ai": AreaEnum.OVERSEAS,
    "dwz.date": AreaEnum.OVERSEAS,
    "fb.gg": AreaEnum.OVERSEAS,
    "bili2233.cn": AreaEnum.DOMESTIC,
}

DOMAIN_DICT = {
    "douyin": SiteEnum.DOUYIN,
    "iesdouyin": SiteEnum.DOUYIN,
    "amemv": SiteEnum.DOUYIN,
    "douyinshortvideo": SiteEnum.DOUYIN,
    "dyshortvideo": SiteEnum.DOUYIN,
    "tiktokv": SiteEnum.TIKTOK,
    "tiktokcdn": SiteEnum.TIKTOK,
    "tiktok": SiteEnum.TIKTOK,
    "musical": SiteEnum.TIKTOK,
    "musemuse": SiteEnum.TIKTOK,
    "muscdn": SiteEnum.TIKTOK,
    "vigovideo": SiteEnum.TIKTOK,
    "lemon8-app": SiteEnum.LEMON8,
    "instagram": SiteEnum.INSTAGRAM,
    "threads": SiteEnum.THREADS,
    "facebook": SiteEnum.FACEBOOK,
    "fb": SiteEnum.FACEBOOK,
    "youtube": SiteEnum.YOUTUBE,
    "youtu": SiteEnum.YOUTUBE,
    "vimeo": SiteEnum.VIMEO,
    "vk": SiteEnum.VK,
    "dailymotion": SiteEnum.DAILYMOTION,
    "twitter": SiteEnum.TWITTER,
    "x": SiteEnum.TWITTER,
    "hao222": SiteEnum.QUANMIN,
    "quanmin.baidu": SiteEnum.QUANMIN,
    "xspshare.baidu": SiteEnum.QUANMIN,
    "v.baidu": SiteEnum.QUANMIN,
    "haokan.baidu": SiteEnum.QUANMIN,
    "sv.baidu": SiteEnum.QUANMIN,
    "mbd.baidu": SiteEnum.QUANMIN,  # 百家号 vnil支持 https://mbd.baidu.com/newspage/data/videolanding?nid=sv_4387083470244316359&sourceFrom=share
    "haokan.hao123": SiteEnum.QUANMIN,
    "mr.baidu": SiteEnum.QUANMIN,
    "gifshow": SiteEnum.KUAISHOU,
    "kuaishou": SiteEnum.KUAISHOU,
    "kuaishouapp": SiteEnum.KUAISHOU,
    "yxixy": SiteEnum.KUAISHOU,
    "chenzhongtech": SiteEnum.KUAISHOU,
    "kw": SiteEnum.KUAISHOU,
    "kwai": SiteEnum.KWAI,
    "kwai-video": SiteEnum.KWAI,
    "miaopai": SiteEnum.WEIBO,
    "xiaokaxiu": SiteEnum.WEIBO,
    "yixia": SiteEnum.WEIBO,
    "weibo": SiteEnum.WEIBO,
    "weico": SiteEnum.WEIBO,
    "t.cn": SiteEnum.WEIBO,
    "toutiao": SiteEnum.TOUTIAO,
    "365yg": SiteEnum.TOUTIAO,
    "ixigua": SiteEnum.TOUTIAO,
    "xiguaapp": SiteEnum.TOUTIAO,
    "xiguavideo": SiteEnum.TOUTIAO,
    "xiguashipin": SiteEnum.TOUTIAO,
    "pstatp": SiteEnum.TOUTIAO,
    "zijiecdn": SiteEnum.TOUTIAO,
    "zijieimg": SiteEnum.TOUTIAO,
    "toutiaocdn": SiteEnum.TOUTIAO,
    "toutiaoimg": SiteEnum.TOUTIAO,
    "toutiao12": SiteEnum.TOUTIAO,
    "toutiao11": SiteEnum.TOUTIAO,
    "neihanshequ": SiteEnum.TOUTIAO,
    "hulushequ": SiteEnum.PIPIXIA,
    "pipix": SiteEnum.PIPIXIA,
    "immomo": SiteEnum.MOMO,
    "momocdn": SiteEnum.MOMO,
    "xiaohongshu": SiteEnum.XIAOHONGSHU,
    "xhsurl": SiteEnum.XIAOHONGSHU,
    "xhslink": SiteEnum.XIAOHONGSHU,
    "xhscdn": SiteEnum.XIAOHONGSHU,
    "bilibili": SiteEnum.BILIBILI,
    "b23": SiteEnum.BILIBILI,
    "bili2233": SiteEnum.BILIBILI,
    "acfun": SiteEnum.ACFUN,
    "shua8cn": SiteEnum.YJJX,
    "shuabaoba": SiteEnum.YJJX,

    # "huya": SiteEnum.FLVCD,
    # "douyu": SiteEnum.FLVCD,
    # "tangdou": SiteEnum.FLVCD,
    # "krcom": SiteEnum.FLVCD,
    # "huxiu": SiteEnum.FLVCD,
    # "xinhuanet": SiteEnum.FLVCD,  # 新华网
    # "news": SiteEnum.FLVCD,  # 新华网

    "meipai": SiteEnum.MEIPAI,
    "wide.meipai": SiteEnum.MEIPAI,
    "meitu": SiteEnum.MEITU,
    "music.163": SiteEnum.YUNYINYUE,
    "xiaoying": SiteEnum.XIAOYING,
    "vivavideo": SiteEnum.XIAOYING,
    "weishi.qq": SiteEnum.WEISHI,
    "qzone.qq": SiteEnum.WEISHI,
    "kg4.qq": SiteEnum.KG,
    "kg3.qq": SiteEnum.KG,
    "kg2.qq": SiteEnum.KG,
    "kg1.qq": SiteEnum.KG,
    "kg.qq": SiteEnum.KG,
    "suno": SiteEnum.SUNO,
    "mp.qq": SiteEnum.KANDIAN,
    "html5.qq": SiteEnum.KANDIAN,
    "kandian.qq": SiteEnum.KANDIAN,
    "weixin.qq": SiteEnum.WXMP,
    "v.qq": SiteEnum.TENCENT,
    # 'youku': SiteEnum.YOUKU,
    "iqiyi": SiteEnum.IQIYI,
    "sohu": SiteEnum.SOHU,
    "tb": SiteEnum.YJJX,
    "tmall": SiteEnum.YJJX,
    "taobao": SiteEnum.YJJX,
    "1688": SiteEnum.YJJX,
    "alibaba": SiteEnum.YJJX,
    "ulikecam": SiteEnum.ULIKECAM,
    "uc": SiteEnum.UC,
    "izuiyou": SiteEnum.ZUIYOU,
    "xiaochuankeji": SiteEnum.ZUIYOU,
    "pearvideo": SiteEnum.PEARVIDEO,
    "tumblr": SiteEnum.TUMBLR,
    "luisonte": SiteEnum.TUMBLR,
    "pornhubpremium": SiteEnum.PORNHUB,
    "pornhub": SiteEnum.PORNHUB,
    "xvideos": SiteEnum.XVIDEOS,
    "redd": SiteEnum.REDDIT,
    "reddit": SiteEnum.REDDIT,
    "ippzone": SiteEnum.PIPIGX,
    "pipigx": SiteEnum.PIPIGX,
    "yinyuetai": SiteEnum.YINYUETAI,
    "qiushibaike": SiteEnum.YJJX,
    "eyepetizer": SiteEnum.YJJX,
    "xunlei": SiteEnum.YJJX,
    # 'jd': SiteEnum.JD,
    "dewu": SiteEnum.YJJX,
    "poizon": SiteEnum.YJJX,
    # 'yangkeduo': SiteEnum.PDD,
    # 'pinduoduo': SiteEnum.PDD,
    "likee": SiteEnum.LIKEE,
    "triller": SiteEnum.TRILLER,
    "pinterest": SiteEnum.PINTEREST,
    "pin": SiteEnum.PINTEREST,  # https://pin.it/33qOUQU
    "cctv": SiteEnum.CCTV,
    "cntv": SiteEnum.CCTV,
    "ncpa-classic": SiteEnum.YT_DLP_DOMESTIC,
    "56": SiteEnum.YT_DLP_DOMESTIC,
    "mgtv": SiteEnum.YT_DLP_DOMESTIC,
    "amazon.co": SiteEnum.YT_DLP_OVERSEAS,
    "amazon": SiteEnum.YT_DLP_OVERSEAS,
    "amzn": SiteEnum.YT_DLP_OVERSEAS,  # amzn.to 亚马逊短地址
    "youporn": SiteEnum.YT_DLP_OVERSEAS,
    "sxyprn": SiteEnum.YT_DLP_OVERSEAS,
    "xxxymovies": SiteEnum.YT_DLP_OVERSEAS,
    "xnxx": SiteEnum.YT_DLP_OVERSEAS,
    "xhamster": SiteEnum.YT_DLP_OVERSEAS,
    "xhamster2": SiteEnum.YT_DLP_OVERSEAS,
    "beeg": SiteEnum.YT_DLP_OVERSEAS,
    "redtube": SiteEnum.YT_DLP_OVERSEAS,
    "vimeopro": SiteEnum.YT_DLP_OVERSEAS,
    "vine": SiteEnum.YT_DLP_OVERSEAS,
    "naver": SiteEnum.YT_DLP_OVERSEAS,
    "imdb": SiteEnum.YT_DLP_OVERSEAS,
    "yahoo": SiteEnum.YT_DLP_OVERSEAS,
    "businessinsider": SiteEnum.YT_DLP_OVERSEAS,
    "bbc.co": SiteEnum.YT_DLP_OVERSEAS,
    "cnn": SiteEnum.YT_DLP_OVERSEAS,
    "vlive": SiteEnum.YT_DLP_OVERSEAS,
    "viki": SiteEnum.YT_DLP_OVERSEAS,
    "afreecatv": SiteEnum.YT_DLP_OVERSEAS,
    "twitch": SiteEnum.YT_DLP_OVERSEAS,
    "snapchat": SiteEnum.YT_DLP_OVERSEAS,
    "avgle": SiteEnum.YT_DLP_OVERSEAS,
    "manporn": SiteEnum.YT_DLP_OVERSEAS,
}


def get_site_enum_by_url(url):
    """
    优先匹配子域名
    """
    return DOMAIN_DICT.get(
        urls.get_base_domain_with_suffix_from_url(url),
        DOMAIN_DICT.get(
            urls.get_sub_domain_from_url(url),
            DOMAIN_DICT.get(urls.get_base_domain_from_url(url)),
        ),
    )
