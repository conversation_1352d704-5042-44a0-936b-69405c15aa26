import logging
import threading
import httpx
from knifes import times
from pkg.config import settings

logger = logging.getLogger(__name__)


# 打印日志不是在协程环境中，此处使用threading.Thread发送飞书消息，而不是asyncio.create_task
class CriticalAlarmFilter(logging.Filter):
    def filter(self, record):
        if record.levelno == logging.CRITICAL:
            msg = f"[{record.module}]{record.getMessage()}"
            if settings.node_name:
                msg = f"[{settings.node_name}]" + msg
            threading.Thread(target=send_feishu_msg(msg)).start()  # send feishu msg, no need to wait
        return True


def send_feishu_msg(msg):
    data = {
        "msg_type": "text",
        "content": {"text": f"{msg} [{times.strftime()}]"},
    }
    try:
        httpx.post(settings.feishu_alarm_api, json=data, verify=False)
    except:  # noqa
        logger.exception(f"send feishu msg failed, {msg}")
