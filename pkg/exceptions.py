from enum import Enum


class ExtractErrorEnum(Enum):
    UNKNOWN_ERROR = (-1, "发生未知错误，请稍后重试", "Unknown", True)

    REQUEST_TIMEOUT_ERROR = (-8, "请求超时，请重试", "Timeout", True)
    INVALID_URL_ERROR = (-9, "链接格式错误，请输入正确的链接", "InvalidURL")

    # [-30, -10] 客户端建议用户使用嗅探功能
    COMMON_URL_ERROR = (-10, "提取失败,请检查输入的链接是否正确", "ExtractFailed", True)
    UNSUPPORTED_URL_ERROR = (-11, "提取失败,请检查输入的链接是否正确", "UnsupportedURL")
    PRIVATE_URL_ERROR = (-12, "暂不支持提取私密链接内容", "NonPublicContent")
    GEO_RESTRICTED_ERROR = (-13, "该链接存在地区限制，暂不支持提取", "GeoRestricted")
    NOT_SUPPORT_LIVE_ERROR = (-14, "暂不支持提取直播间(正在直播)的视频", "LiveStreamNotSupported")
    NOT_SUPPORT_BATCH_ERROR = (-15, "暂不支持主页批量提取，请输入单个帖子链接", "PlaylistNotSupported")
    USER_NOT_EXIST_ERROR = (-16, "用户不存在，请检查输入的链接是否正确", "UserNotFound")
    NOT_SUPPORT_REGION_ERROR = (-17, "暂不支持此地区，请联系客服反馈", "")
    NO_STORY_ERROR = (-18, "用户没有正在显示的快拍", "NoStory")
    DELETED_CONTENT_ERROR = (-19, "请检查输入的链接内容是否已被删除", "ContentDeleted")
    INVALID_USER_PAGE_URL_ERROR = (-20, "请输入作者主页链接", "InvalidUserPageURL")
    INVALID_USER_PAGE_OR_PLAYLIST_URL_ERROR = (-21, "请输入公开的频道页/播放列表页链接", "InvalidPlaylistURL")
    REVIEWING_CONTENT_ERROR = (-22, "请检查输入的链接内容是否在审核中或已被删除", "")
    UNSUPPORTED_SITE_ERROR = (-23, "暂不支持此链接，请联系客服反馈", "UnsupportedSite")
    VIP_URL_ERROR = (-24, "暂不支持提取非公开内容(会员内容)", "PremiumContentNotSupported")
    NOT_YET_PREMIERED_ERROR = (-25, "内容还未上映", "ContentNotPremiered")
    ILLEGAL_RESOURCES_ERROR = (-26, "不支持敏感/违规资源", "")

    RETRYABLE_ERROR = (-30, "处理失败，请重试", "Retryable", True)  # 被反爬的原因

    def __init__(self, code: int, message: str, code_str: str, retryable: bool = False):
        self.code = code
        self.message = message
        self.code_str = code_str
        self.retryable = retryable

    @classmethod
    def get_by_code(cls, code):
        return next(filter(lambda x: x.code == code, cls.__members__.values()), None)

    @classmethod
    def get_by_code_str(cls, code_str):
        return next(filter(lambda x: x.code_str == code_str, cls.__members__.values()), None)


class ExtractError(Exception):
    def __init__(self, error_enum: ExtractErrorEnum, detail: str | None = None):
        self.error_enum = error_enum or ExtractErrorEnum.COMMON_URL_ERROR
        self.detail = detail
        super().__init__(self.error_enum.message)
