#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试B站yt-dlp提取器
"""

import asyncio
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.getcwd())

from extractor.bilibili.yt_dlp import main
from models import ExtractReq
from pkg.exceptions import ExtractError

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


async def test_bilibili_extraction():
    """测试B站视频提取功能"""
    test_urls = [
        "https://space.bilibili.com/10119428",  # 测试用户主页
        # 可以添加更多测试URL
    ]
    
    for url in test_urls:
        logger.info(f"开始测试URL: {url}")
        
        try:
            # 测试第一页
            req = ExtractReq(url=url, extractor="bilibili", cursor="0")
            result = await main(req)
            
            logger.info(f"✅ 提取成功")
            logger.info(f"用户信息: {result.user.username if result.user else 'None'}")
            logger.info(f"视频数量: {len(result.posts)}")
            logger.info(f"是否有更多: {result.has_more}")
            logger.info(f"下一页游标: {result.next_cursor}")
            
            # 显示前3个视频
            for i, post in enumerate(result.posts[:3]):
                logger.info(f"视频{i+1}: {post.text} (ID: {post.id})")
                if post.medias:
                    logger.info(f"  URL: {post.medias[0].resource_url}")
            
            # 如果有更多内容，测试第二页
            if result.has_more and result.next_cursor != "no_more":
                logger.info("测试第二页...")
                req2 = ExtractReq(url=url, extractor="bilibili", cursor=result.next_cursor)
                result2 = await main(req2)
                logger.info(f"第二页视频数量: {len(result2.posts)}")
                
            logger.info("=" * 80)
            
        except ExtractError as e:
            logger.error(f"❌ 提取失败 (ExtractError): {e.error_enum.message}")
            logger.error(f"详细信息: {e.detail}")
        except Exception as e:
            logger.error(f"❌ 提取失败 (其他错误): {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_bilibili_extraction())
