import logging

from httpx import Timeout
from knifes import useragent

from constants import client

logger = logging.getLogger(__name__)
# https://en.wikipedia.org/wiki/List_of_HTTP_status_codes#3xx_redirection
redirection_http_status = {301, 302, 303, 307, 308}


async def get_long_url(short_url, *, user_agent=""):
    """
    user_agent # 默认''   t.co如果设置的浏览器ua会返回200
    """
    try:
        # 移除非打印ASCII字符 (ASCII 32-126 是可打印字符)
        # https://linear.app/gys/issue/GYS-30/invalid-non-printable-ascii-character-in-url
        cleaned_url = "".join(char for char in short_url if 32 <= ord(char) <= 126)
        resp = await client.head(
            cleaned_url,
            headers={"user-agent": useragent.DEFAULT if user_agent is None else user_agent, "connection": "close"},
            timeout=Timeout(3),
            follow_redirects=True,
        )
        return str(resp.url)
    except Exception as e:  # noqa
        logger.critical(f"get_long_url fail, short_url:{short_url}, error: {e}")
        return short_url


async def get_redirect_location(url, *, user_agent="", timeout=Timeout(3), ignore_error=False, request_by_get=False, http_client=client):
    try:
        resp = await http_client.request(
            "GET" if request_by_get else "HEAD",
            url,
            headers=useragent.ua_headers(user_agent),
            timeout=timeout,
        )
        if resp.status_code in redirection_http_status and "location" in resp.headers:
            return resp.headers.get("location")
        return url
    except:  # noqa
        if not ignore_error:
            logger.critical(f"get_redirect_location fail, url:{url}", exc_info=True)
        return url
