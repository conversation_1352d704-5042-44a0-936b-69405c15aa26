import importlib
import logging

from fastapi import APIRouter
from fastapi.encoders import jsonable_encoder
from httpx import TimeoutException
from models import ExtractReq
from pkg.exceptions import ExtractError, ExtractErrorEnum

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/extract/post")
async def extract_post(req: ExtractReq):
    try:
        m = importlib.import_module(f"extractor.{req.extractor}")
        result = await getattr(m, "main")(req)
        if not result or result.is_empty():
            raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

        logger.info(
            f"[{req.extractor}][{req.source}] url:{req.url}, result:{jsonable_encoder(result)}"
        )
        return result
    except ExtractError as e:
        logger.exception(f"[{req.extractor}] url:{req.url}, fail")
        raise e
    except TimeoutException as e:
        logger.exception(f"[{req.extractor}] url:{req.url}, fail")
        raise ExtractError(ExtractErrorEnum.REQUEST_TIMEOUT_ERROR, detail=str(e))
    except Exception as e:
        logger.exception(f"[{req.extractor}] url:{req.url}, fail")
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail=str(e))


@router.post("/extract/playlist")
async def extract_playlist(req: ExtractReq):
    try:
        m = importlib.import_module(f"extractor.{req.extractor}")
        result = await getattr(m, "main")(req)
        logger.info(
            f"[{req.extractor}][{req.source}] url:{req.url}, result:{jsonable_encoder(result)}"
        )
        return result
    except ExtractError as e:
        logger.exception(f"[{req.extractor}] url:{req.url}, fail")
        raise e
    except TimeoutException as e:
        logger.exception(f"[{req.extractor}] url:{req.url}, fail")
        raise ExtractError(ExtractErrorEnum.REQUEST_TIMEOUT_ERROR, detail=str(e))
    except Exception as e:
        logger.exception(f"[{req.extractor}] url:{req.url}, fail")
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail=str(e))
