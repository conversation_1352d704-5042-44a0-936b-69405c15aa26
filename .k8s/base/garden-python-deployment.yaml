apiVersion: apps/v1
kind: Deployment
metadata:
  name: garden-python
  labels:
    app: garden-python
spec:
  replicas: 2
  selector:
    matchLabels:
      app: garden-python
  template:
    metadata:
      labels:
        app: garden-python
    spec:
      containers:
        - name: garden-python
          image: registry.cn-shenzhen.aliyuncs.com/gystech/garden-python:latest
          ports:
            - containerPort: 80
          env:
            - name: REDIS_URL
              value: redis://redis:6379
            - name: NODE_NAME
              valueFrom:
                configMapKeyRef:
                  name: garden-config
                  key: NODE_NAME
      imagePullSecrets:
        - name: acr-docker-registry
---
apiVersion: v1
kind: Service
metadata:
  name: garden
spec:
  selector:
    app: garden-python
  ports:
    - port: 80
      targetPort: 80
      nodePort: 30081
  type: NodePort
