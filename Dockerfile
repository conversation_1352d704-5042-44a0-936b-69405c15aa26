FROM python:3.10.14-alpine AS base
# 设置时区
RUN apk update && apk add --no-cache tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo "Asia/Shanghai" > /etc/timezone && apk del tzdata 
# 生成截图功能需要用到字体
# 如果需要更多 CJK 字形或变体，可以添加 font-noto-cjk-extra
# 对于额外的符号和特殊字符，可以添加 font-noto-extra
# fontconfig 是一个用于配置和自定义字体访问的库和实用工具集, 它支持自动检测系统中安装的字体、根据应用程序的请求选择最佳匹配的字体
RUN apk add --no-cache \
  font-noto \
  font-noto-cjk \
  fontconfig \
  && fc-cache -f \
  && rm -rf /var/cache/apk/*
# 拷贝 apache2/mime.types 文件
RUN mkdir -p /etc/apache2 
COPY ./vendor/mime.types /etc/apache2/mime.types


FROM base AS runner
WORKDIR /app
COPY requirements.txt ./
RUN pip install --no-cache-dir --upgrade -r requirements.txt
COPY . .

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "80", "--proxy-headers", "--no-access-log"]
