from dataclasses import dataclass, field

from knifes.media import MediaType, get_quality_note_by_quality
from knifes.urls import sanitize_url
from pydantic import BaseModel, computed_field


class ExtractReq(BaseModel):
    url: str  # 网址
    extractor: str  # 提取器
    cursor: str | None = None  # 游标(用于播放列表的分页)
    source: str = "unknown"  # 请求来源的应用/产品


@dataclass
class User:
    username: str | None = None  # 用户名
    avatar: str | None = None  # 头像


@dataclass
class Stats:
    comment_count: int | None = None  # 评论数
    digg_count: int | None = None  # 点赞数
    play_count: int | None = None  # 播放数
    share_count: int | None = None  # 分享数


class Format(BaseModel):
    # media quality, such as video resolution,  e.g. 720、1080、2160、4320、...
    quality: int
    video_url: str | None
    video_ext: str | None = None
    video_size: int | None = None
    audio_url: str | None = None
    audio_ext: str | None = None
    audio_size: int | None = None
    separate: int = 0

    @computed_field
    def quality_note(self) -> str:
        return get_quality_note_by_quality(self.quality)

    @classmethod
    def audio(cls, audio_url, audio_ext, audio_size=0):
        return Format(
            quality=0,
            video_url=None,
            audio_url=audio_url,
            audio_ext=audio_ext,
            audio_size=audio_size,
        )


class Media:
    preview_proxy_url: str | None = None  # 封面图(代理地址)
    formats: list[Format] | None = None
    headers: dict[str, str] | None = None  # 请求头

    def __init__(self, media_type: MediaType, resource_url, preview_url=None):
        self.media_type = media_type  # image、video、audio
        self.resource_url = sanitize_url(resource_url)  # 资源下载地址
        self.preview_url = sanitize_url(preview_url)  # 封面图(可能无)

    @classmethod
    def image(cls, image_url):
        return Media(media_type=MediaType.IMAGE, resource_url=image_url)

    @classmethod
    def video(cls, video_url, cover_url=None):
        return Media(media_type=MediaType.VIDEO, resource_url=video_url, preview_url=cover_url)

    @classmethod
    def audio(cls, audio_url, cover_url=None):
        return Media(media_type=MediaType.AUDIO, resource_url=audio_url, preview_url=cover_url)


@dataclass
class Post:
    text: str | None = None  # 文案
    medias: list[Media] = field(default_factory=list)  #  媒体列表
    stats: Stats | None = None  # 统计信息
    overseas: int = 0  # 否海外资源 0: 否 1: 是
    id: str | None = None  # 帖子ID
    create_time: str | None = None  # 创建时间

    def is_empty(self):
        return not self.medias

    @classmethod
    def single_media(cls, media: Media, text=None):
        return Post(text=text, medias=[media])


@dataclass
class Playlist:
    next_cursor: str = "no_more"
    has_more: bool = False
    posts: list[Post] = field(default_factory=list)
    user: User | None = None
    overseas: int = 0
