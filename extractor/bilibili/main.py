#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
B站主页视频提取工具
使用 yt-dlp 包提取 B站主页链接中的视频信息

示例用法:
    python main.py https://space.bilibili.com/10119428
"""

import sys
import json
import logging
from typing import Dict, List, Optional, Any
import yt_dlp


class BilibiliSpaceExtractor:
    """B站主页视频提取器"""

    def __init__(self, verbose: bool = True, extract_flat: bool = True):
        """
        初始化提取器

        Args:
            verbose: 是否启用详细输出
            extract_flat: 是否只提取基本信息（更快，但信息较少）
        """
        self.verbose = verbose
        self.extract_flat = extract_flat
        self.setup_logging()

    def setup_logging(self):
        """设置日志"""
        level = logging.DEBUG if self.verbose else logging.INFO
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_ydl_options(self) -> Dict[str, Any]:
        """
        获取 yt-dlp 配置选项

        Returns:
            yt-dlp 配置字典
        """
        options = {
            'quiet': not self.verbose,  # 使用 quiet 而不是 verbose
            'no_warnings': not self.verbose,
            'extract_flat': self.extract_flat,  # 根据设置决定是否只提取基本信息
            'ignoreerrors': True,  # 忽略单个视频的错误，继续处理其他视频
            'playlist_items': '1-10' if not self.extract_flat else '1-20',  # 详细模式减少数量
            'socket_timeout': 30,  # 设置超时时间
            'retries': 3,  # 重试次数
            # 设置用户代理，模拟浏览器访问
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://www.bilibili.com/',
            },
            # 尝试禁用代理（如果有的话）
            'proxy': '',
        }
        return options

    def extract_space_info(self, space_url: str) -> Optional[Dict[str, Any]]:
        """
        提取B站主页信息

        Args:
            space_url: B站主页URL，如 https://space.bilibili.com/10119428

        Returns:
            提取的信息字典，失败时返回 None
        """
        self.logger.info(f"开始提取B站主页信息: {space_url}")

        try:
            with yt_dlp.YoutubeDL(self.get_ydl_options()) as ydl:
                # 提取信息但不下载
                info = ydl.extract_info(space_url, download=False)

                if info is None:
                    self.logger.error("未能提取到任何信息")
                    return None

                # 清理和序列化信息
                clean_info = ydl.sanitize_info(info)
                self.logger.info(f"成功提取信息，共找到 {len(clean_info.get('entries', []))} 个视频")

                return clean_info

        except yt_dlp.DownloadError as e:
            error_msg = str(e)
            self.logger.error(f"yt-dlp 下载错误: {error_msg}")

            # 提供具体的错误解决建议
            if "Failed to parse JSON" in error_msg:
                self.logger.error("可能的原因：网络连接问题或B站反爬机制")
                self.logger.error("建议：1. 检查网络连接 2. 稍后重试 3. 使用浏览器cookies")
            elif "Request is rejected" in error_msg or "need to login" in error_msg:
                self.logger.error("需要登录才能访问该主页")
                self.logger.error("建议：使用 --cookies-from-browser 参数")
            elif "timeout" in error_msg.lower():
                self.logger.error("网络超时")
                self.logger.error("建议：检查网络连接或稍后重试")

            return None
        except Exception as e:
            self.logger.error(f"提取过程中发生未知错误: {e}")
            self.logger.error("建议：检查URL格式是否正确")
            return None

    def format_video_info(self, video_entry: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化单个视频信息

        Args:
            video_entry: 视频条目信息

        Returns:
            格式化后的视频信息
        """
        return {
            'id': video_entry.get('id', 'N/A'),
            'title': video_entry.get('title', 'N/A'),
            'url': video_entry.get('url', 'N/A'),
            'webpage_url': video_entry.get('webpage_url', 'N/A'),
            'duration': video_entry.get('duration', 'N/A'),
            'view_count': video_entry.get('view_count', 'N/A'),
            'upload_date': video_entry.get('upload_date', 'N/A'),
            'uploader': video_entry.get('uploader', 'N/A'),
            'description': video_entry.get('description', 'N/A')[:200] + '...' if video_entry.get('description') and len(video_entry.get('description', '')) > 200 else video_entry.get('description', 'N/A')
        }

    def print_results(self, info: Dict[str, Any]):
        """
        打印提取结果

        Args:
            info: 提取的信息字典
        """
        print("\n" + "="*80)
        print("B站主页视频提取结果")
        print("="*80)

        # 打印主页基本信息
        print(f"主页标题: {info.get('title', 'N/A')}")
        print(f"主页URL: {info.get('webpage_url', 'N/A')}")
        print(f"UP主: {info.get('uploader', 'N/A')}")
        print(f"视频总数: {len(info.get('entries', []))}")

        # 打印视频列表
        entries = info.get('entries', [])
        if entries:
            print(f"\n视频列表 (显示前{min(10, len(entries))}个):")
            print("-" * 80)

            for i, entry in enumerate(entries[:10], 1):
                formatted_info = self.format_video_info(entry)
                print(f"\n{i}. {formatted_info['title']}")
                print(f"   视频ID: {formatted_info['id']}")
                print(f"   链接: {formatted_info['webpage_url']}")
                print(f"   时长: {formatted_info['duration']}")
                print(f"   播放量: {formatted_info['view_count']}")
                print(f"   上传日期: {formatted_info['upload_date']}")
                if formatted_info['description'] != 'N/A':
                    print(f"   描述: {formatted_info['description']}")
        else:
            print("\n未找到任何视频")

        print("\n" + "="*80)

    def save_to_json(self, info: Dict[str, Any], filename: str = "bilibili_space_info.json"):
        """
        将提取的信息保存为JSON文件

        Args:
            info: 提取的信息字典
            filename: 保存的文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(info, f, ensure_ascii=False, indent=2)
            self.logger.info(f"信息已保存到文件: {filename}")
        except Exception as e:
            self.logger.error(f"保存文件时发生错误: {e}")


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python main.py <B站主页URL>")
        print("示例: python main.py https://space.bilibili.com/10119428")
        sys.exit(1)

    space_url = sys.argv[1]

    # 验证URL格式
    if not space_url.startswith('https://space.bilibili.com/'):
        print("错误: 请提供有效的B站主页URL")
        print("格式: https://space.bilibili.com/用户ID")
        sys.exit(1)

    # 创建提取器并执行提取
    extractor = BilibiliSpaceExtractor(verbose=True)

    print(f"正在提取B站主页信息: {space_url}")
    print("请稍候...")

    info = extractor.extract_space_info(space_url)

    if info:
        extractor.print_results(info)
        extractor.save_to_json(info)
        print("\n提取完成！")
    else:
        print("\n提取失败！请检查URL是否正确，或者该主页是否需要登录访问。")
        print("\n如果需要登录访问，可以尝试以下方法：")
        print("1. 使用 --cookies-from-browser 参数从浏览器导入cookies")
        print("2. 手动提供cookies文件")
        print("3. 检查网络连接和防火墙设置")
        sys.exit(1)


if __name__ == "__main__":
    main()