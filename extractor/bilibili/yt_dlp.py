import logging
from asyncer import asyncify
import yt_dlp
from models import ExtractReq, Playlist, User, Media, Post
from pkg.exceptions import ExtractError, ExtractErrorEnum

logger = logging.getLogger(__name__)


async def main(req: ExtractReq) -> Playlist:
    """
    提取B站用户主页或合集视频列表
    支持链接格式:
    - https://space.bilibili.com/10119428 (用户主页)
    - https://space.bilibili.com/37029661/lists/3018?type=season (合集)
    """
    try:
        result = await asyncify(_extract_playlist)(req.url)
        return _convert_to_user_posts_result(result, req)
    except Exception as e:
        logger.exception(f"提取B站用户页面失败: {req.url}")
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail=str(e))


def _extract_playlist(url: str):
    """同步提取播放列表信息"""
    ydl_opts = {
        "extract_flat": True,  # 只提取基本信息，不下载
        "quiet": True,
        "no_check_certificate": True,
        # 移除cookiesfrombrowser配置，避免依赖浏览器cookies
        "playlistend": 100,  # 最多提取100个，用于分页切片
        # 添加用户代理，模拟正常浏览器访问
        "http_headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        },
    }

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            return ydl.extract_info(url, download=False)
    except Exception as e:
        logger.error(f"yt-dlp提取失败: {str(e)}")
        # 如果第一次失败，尝试不使用extract_flat
        try:
            ydl_opts.pop("extract_flat", None)
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                return ydl.extract_info(url, download=False)
        except Exception as e2:
            logger.error(f"第二次尝试也失败: {str(e2)}")
            raise e2


def _convert_to_user_posts_result(result: dict, params: ExtractReq) -> Playlist:
    """将yt-dlp结果转换为UserPostsResult格式"""
    if not result:
        return Playlist()

    # 提取用户信息
    user_info = None
    if uploader := result.get("uploader"):
        user_info = User(username=uploader, avatar=result.get("uploader_url"))

    # 处理分页
    cursor = params.cursor or 0
    if isinstance(cursor, str):
        cursor = int(cursor) if cursor.isdigit() else 0

    entries = result.get("entries", [])
    page_size = 10
    start_idx = cursor
    end_idx = start_idx + page_size

    # 获取当前页数据
    current_page_entries = entries[start_idx:end_idx]

    # 构建posts列表
    posts = []
    for entry in current_page_entries:
        if not entry:
            continue

        # 构建视频URL
        video_url = entry.get("url") or f"https://www.bilibili.com/video/{entry.get('id', '')}"

        # 创建MediaInfo
        media = Media.video(video_url=video_url, cover_url=entry.get("thumbnail"))

        # 创建PostInfo
        post = Post(id=entry.get("id"), text=entry.get("title"), medias=[media])
        posts.append(post)

    # 判断是否还有更多内容
    has_more = end_idx < len(entries)
    next_cursor = str(end_idx) if has_more else "no_more"

    return Playlist(next_cursor=next_cursor, has_more=has_more, posts=posts, user=user_info, overseas=0)
