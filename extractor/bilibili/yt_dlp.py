"""
B站yt-dlp提取器
使用 yt-dlp 包提取 B站用户主页视频信息
"""

import logging
from asyncer import asyncify
import yt_dlp
from models import ExtractReq, Playlist, User, Media, Post
from pkg.exceptions import ExtractError, ExtractErrorEnum

logger = logging.getLogger(__name__)


async def main(req: ExtractReq) -> Playlist:
    """
    提取B站用户主页或合集视频列表
    支持链接格式:
    - https://space.bilibili.com/10119428 (用户主页)
    - https://space.bilibili.com/37029661/lists/3018?type=season (合集)
    """
    try:
        result = await asyncify(_extract_playlist)(req.url)
        if result is None:
            logger.error("提取结果为空")
            raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail="提取结果为空")
        return _convert_to_user_posts_result(result, req)
    except ExtractError:
        # 重新抛出ExtractError
        raise
    except Exception as e:
        logger.exception(f"提取B站用户页面失败: {req.url}")
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR, detail=str(e))


def _get_ydl_options(extract_flat: bool = True, verbose: bool = False, attempt: int = 1) -> dict:
    """
    获取 yt-dlp 配置选项，针对B站反爬机制进行优化

    Args:
        extract_flat: 是否只提取基本信息（更快，但信息较少）
        verbose: 是否启用详细输出
        attempt: 尝试次数，用于调整配置

    Returns:
        yt-dlp 配置字典
    """
    # 基础配置
    options = {
        'quiet': not verbose,
        'no_warnings': not verbose,
        'extract_flat': extract_flat,
        'ignoreerrors': True,
        'playlist_items': '1-10' if not extract_flat else '1-20',
        'socket_timeout': 60,  # 增加超时时间
        'retries': 5,  # 增加重试次数
        'fragment_retries': 5,
        'skip_unavailable_fragments': True,
        'no_check_certificate': True,
        # 禁用代理
        'proxy': '',
    }

    # 根据尝试次数调整配置
    if attempt == 1:
        # 第一次尝试：标准浏览器配置
        options['http_headers'] = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0',
        }
    elif attempt == 2:
        # 第二次尝试：移动端配置
        options['http_headers'] = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
        }
        options['sleep_interval'] = 2
        options['max_sleep_interval'] = 5
    elif attempt == 3:
        # 第三次尝试：简化配置
        options['http_headers'] = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        }
        options['sleep_interval'] = 3
        options['max_sleep_interval'] = 8
        options['socket_timeout'] = 120

    return options


def _extract_playlist(url: str):
    """同步提取播放列表信息，使用多种策略对抗反爬机制"""
    logger.info(f"开始提取B站主页信息: {url}")

    # 尝试不同的配置策略
    strategies = [
        (True, False, 1, "标准extract_flat模式"),
        (True, True, 2, "移动端extract_flat模式"),
        (False, True, 3, "详细模式+简化配置"),
    ]

    last_error = None

    for extract_flat, verbose, attempt, description in strategies:
        logger.info(f"第{attempt}次尝试: {description}")

        try:
            ydl_opts = _get_ydl_options(extract_flat=extract_flat, verbose=verbose, attempt=attempt)

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # 提取信息但不下载
                info = ydl.extract_info(url, download=False)

                if info is None:
                    logger.warning(f"第{attempt}次尝试未能提取到任何信息")
                    continue

                # 清理和序列化信息
                clean_info = ydl.sanitize_info(info)
                entries_count = len(clean_info.get('entries', []))

                if entries_count == 0:
                    logger.warning(f"第{attempt}次尝试提取到的条目数为0")
                    continue

                logger.info(f"第{attempt}次尝试成功，共找到 {entries_count} 个视频")
                return clean_info

        except yt_dlp.DownloadError as e:
            error_msg = str(e)
            logger.error(f"第{attempt}次尝试失败 (DownloadError): {error_msg}")
            last_error = e

            # 分析错误类型
            if "Request is rejected" in error_msg:
                logger.warning("检测到反爬机制，尝试下一种配置...")
                continue
            elif "Failed to parse JSON" in error_msg:
                logger.warning("JSON解析失败，可能是网络问题，尝试下一种配置...")
                continue
            elif "timeout" in error_msg.lower():
                logger.warning("网络超时，尝试下一种配置...")
                continue
            else:
                logger.error(f"未知的DownloadError: {error_msg}")
                continue

        except Exception as e:
            logger.error(f"第{attempt}次尝试失败 (其他错误): {str(e)}")
            last_error = e
            continue

    # 所有策略都失败了
    logger.error("所有提取策略都失败了")

    # 提供详细的错误信息和建议
    if last_error:
        error_msg = str(last_error)
        if "Request is rejected" in error_msg:
            logger.error("建议：1. 稍后重试 2. 使用浏览器cookies 3. 更换网络环境")
        elif "Failed to parse JSON" in error_msg:
            logger.error("建议：1. 检查网络连接 2. 稍后重试")
        elif "timeout" in error_msg.lower():
            logger.error("建议：检查网络连接或稍后重试")

    return None


def _convert_to_user_posts_result(result: dict, params: ExtractReq) -> Playlist:
    """将yt-dlp结果转换为Playlist格式，参考main.py的数据处理方式"""
    if not result:
        logger.warning("提取结果为空")
        return Playlist()

    logger.info(f"开始转换提取结果，原始数据键: {list(result.keys())}")

    # 提取用户信息 - 改进用户信息提取逻辑
    user_info = None
    uploader = result.get("uploader") or result.get("channel") or result.get("uploader_id")
    if uploader:
        # 尝试获取头像URL
        avatar_url = (result.get("uploader_url") or 
                     result.get("channel_url") or 
                     result.get("thumbnail"))
        user_info = User(username=uploader, avatar=avatar_url)
        logger.info(f"提取到用户信息: {uploader}")
    else:
        logger.warning("未能提取到用户信息")

    # 处理分页
    cursor = params.cursor or 0
    if isinstance(cursor, str):
        cursor = int(cursor) if cursor.isdigit() else 0

    entries = result.get("entries", [])
    logger.info(f"总共找到 {len(entries)} 个条目")

    page_size = 10
    start_idx = cursor
    end_idx = start_idx + page_size

    # 获取当前页数据
    current_page_entries = entries[start_idx:end_idx]
    logger.info(f"当前页处理 {len(current_page_entries)} 个条目 (索引 {start_idx}-{end_idx})")

    # 构建posts列表
    posts = []
    for i, entry in enumerate(current_page_entries):
        if not entry:
            logger.warning(f"跳过空条目 {i}")
            continue

        try:
            post = _format_video_entry(entry, i)
            if post:
                posts.append(post)
        except Exception as e:
            logger.error(f"处理视频条目 {i} 时出错: {e}")
            continue

    logger.info(f"成功转换 {len(posts)} 个视频条目")

    # 判断是否还有更多内容
    has_more = end_idx < len(entries)
    next_cursor = str(end_idx) if has_more else "no_more"

    return Playlist(
        next_cursor=next_cursor, 
        has_more=has_more, 
        posts=posts, 
        user=user_info, 
        overseas=0
    )


def _format_video_entry(entry: dict, index: int) -> Post:
    """
    格式化单个视频条目，处理extract_flat模式的数据
    
    Args:
        entry: 视频条目信息
        index: 条目索引
    
    Returns:
        格式化后的Post对象
    """
    if not entry:
        return None
    
    # 提取视频ID
    video_id = entry.get("id") or entry.get("display_id") or ""
    
    # 在extract_flat模式下，title通常为None，使用视频ID作为标题
    title = entry.get("title") or f"B站视频 {video_id}" if video_id else f"视频 {index + 1}"
    
    # 构建视频URL - extract_flat模式下通常有url字段
    video_url = (entry.get("url") or 
                entry.get("webpage_url") or 
                f"https://www.bilibili.com/video/{video_id}" if video_id else "")
    
    if not video_url:
        logger.warning(f"视频条目 {index} 缺少URL信息")
        return None
    
    # 提取封面图 - extract_flat模式下通常没有thumbnail
    thumbnail = (entry.get("thumbnail") or 
                entry.get("thumbnails", [{}])[0].get("url") if entry.get("thumbnails") else None)
    
    # 创建Media对象
    media = Media.video(video_url=video_url, cover_url=thumbnail)
    
    logger.debug(f"视频 {index + 1}: {title}")
    logger.debug(f"  ID: {video_id}, URL: {video_url}")
    
    # 创建Post对象
    post = Post(
        id=video_id,
        text=title,
        medias=[media]
    )
    
    return post
