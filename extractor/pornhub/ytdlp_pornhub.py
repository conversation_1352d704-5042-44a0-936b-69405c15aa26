# import yt_dlp
# from analyse.model import *
# from garden.exceptions import *
# from garden.constants import local_http_proxy
# import logging
# logger = logging.getLogger(__name__)
# quality_list = [1080, 720, 480, 360, 240]
# ydl = yt_dlp.YoutubeDL({'nocheckcertificate': True, 'proxy': local_http_proxy})
#
#
# https://cn.pornhub.com/view_video.php?viewkey=ph5f50e5c2a722e
# def main(link):
#     j = ydl.extract_info(url=link, download=False)
#     r = PostResult(text=j.get('title'))
#     formats = get_formats(j['formats'])
#     if not formats:
#         raise GardenError('未获取到任何视频地址')
#
#     media = MediaInfo.video(formats[0].video_url, j.get('thumbnail'))
#     media.formats = formats
#     r.medias.append(media)
#     return r
#
#
# def get_formats(formats):
#     # 找视频
#     v_dict = {}
#     for f in formats:
#         if f['protocol'] != 'https':
#             continue
#         v_dict[f['format_id']] = f
#
#     # 组装数据
#     formats = []
#     for quality in quality_list:
#         v = v_dict.get(str(quality) + 'p', None)
#         if v is None:
#             continue
#
#         # 组装数据
#         f = VideoFormat(quality=quality, video_url=v['url'], video_ext=v.get('ext'))
#         formats.append(f)
#     formats = sorted(formats, key=lambda i: i.quality, reverse=True)
#     return formats
