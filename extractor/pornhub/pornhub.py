import re

from asyncer import asyncify
import httpx
import yt_dlp
from yt_dlp.extractor.common import InfoExtractor
from yt_dlp.utils import ExtractorError, NO_DEFAULT

from constants import vendor_timeout
from models import ExtractReq, Media, Post, Format
from service.goto import generate_stream_url

quality_list = [1080, 720, 480, 360, 240]
headers = {
    "authority": "cn.pornhub.com",
    "sec-ch-ua": '"Chromium";v="94", "Google Chrome";v="94", ";Not A Brand";v="99"',
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": '"Android"',
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Linux; Android 5.0; SM-G900P Build/LRX21T) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Mobile Safari/537.36",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
    "sec-fetch-site": "none",
    "sec-fetch-mode": "navigate",
    "sec-fetch-user": "?1",
    "sec-fetch-dest": "document",
    "accept-language": "zh-CN,zh;q=0.9",
}
cookies = {"age_verified": "1", "accessAgeDisclaimerPH": "1", "accessAgeDisclaimerUK": "1", "accessPH": "1"}


class MyPornHubIE(InfoExtractor):
    _VALID_URL = r"""(?x)
                       https?://
                           (?:
                               (?:[^/]+\.)?(?P<host>pornhub(?:premium)?\.(?:com|net|org))/(?:(?:view_video\.php|video/show)\?viewkey=|embed/)|
                               (?:www\.)?thumbzilla\.com/video/
                           )
                           (?P<id>[\da-z]+)
                       """

    def __init__(self):
        super().__init__()

    def _real_extract(self, url):
        mobj = re.match(self._VALID_URL, url)
        video_id = mobj.group("id")

        with httpx.Client(verify=False, timeout=vendor_timeout, cookies=cookies) as client:
            webpage = client.get(url, headers=headers).text

            error_msg = self._html_search_regex(
                (
                    r'(?s)<div[^>]+class=(["\'])(?:(?!\1).)*\b(?:removed|userMessageSection)\b(?:(?!\1).)*\1[^>]*>(?P<error>.+?)</div>',
                    r'(?s)<section[^>]+class=["\']noVideo["\'][^>]*>(?P<error>.+?)</section>',
                ),
                webpage,
                "error message",
                default=NO_DEFAULT,
                fatal=False,
                group="error",
            )
            if error_msg:
                error_msg = re.sub(r"\s+", " ", error_msg)
                raise ExtractorError(f"PornHub said: {error_msg}", expected=True, video_id=video_id)

            title = self._html_search_meta("twitter:title", webpage, default=None) or self._html_search_regex(
                (
                    r'(?s)<h1[^>]+class=["\']title["\'][^>]*>(?P<title>.+?)</h1>',
                    r'<div[^>]+data-video-title=(["\'])(?P<title>(?:(?!\1).)+)\1',
                    r'shareTitle["\']\s*[=:]\s*(["\'])(?P<title>(?:(?!\1).)+)\1',
                ),
                webpage,
                "title",
                group="title",
            )

            flashvars = self._parse_json(
                self._search_regex(r"var\s+flashvars_\d+\s*=\s*({.+?});", webpage, "flashvars", default="{}"), video_id
            )
            if not flashvars:
                raise ExtractorError("PornHub said: no flashvars json data", expected=True, video_id=video_id)

            thumbnail = flashvars.get("image_url")
            media_definitions = flashvars.get("mediaDefinitions")
            mp4_definition = next(filter(lambda d: d.get("format") == "mp4", media_definitions), {})
            get_mp4_media_url = mp4_definition.get("videoUrl")
            if not get_mp4_media_url:
                raise ExtractorError("PornHub said: no get_mp4_media_url", expected=True, video_id=video_id)

            headers["referer"] = url
            formats = client.get(get_mp4_media_url, headers=headers).json()

            return {"title": title, "thumbnail": thumbnail, "formats": formats}


ydl = yt_dlp.YoutubeDL({"nocheckcertificate": True})
ydl.add_info_extractor(MyPornHubIE())
ie = ydl.get_info_extractor("MyPornHub")


# https://cn.pornhub.com/view_video.php?viewkey=ph5f50e5c2a722e
async def main(params: ExtractReq):
    result = await asyncify(ie.extract)(params.url)

    r = Post(text=result["title"])
    formats = get_formats(result["formats"])
    if not formats:
        raise ValueError("未获取到任何视频地址")

    media = Media.video(formats[0].video_url, result["thumbnail"])
    media.formats = formats
    r.medias.append(media)
    return r


def get_formats(formats):
    # 找视频
    v_dict = {}
    for f in formats:
        key = f["quality"] + f["format"]
        v_dict[key] = f

    # 组装数据
    formats = []
    for quality in quality_list:
        v = v_dict.get(str(quality) + "mp4", None)
        if v is None:
            continue

        # 组装数据
        video_url = generate_stream_url(v["videoUrl"], cdn=False)
        # video_url = v['videoUrl']
        f = Format(quality=quality, video_url=video_url, video_ext=v["format"])
        formats.append(f)
    formats = sorted(formats, key=lambda i: i.quality, reverse=True)
    return formats
