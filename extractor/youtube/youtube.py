import functools
import logging
import re
from typing import Any

from asyncer import asyncify
import yt_dlp
from knifes import urls
from knifes.strings import safe_int

from pkg.exceptions import ExtractError, ExtractErrorEnum
from models import ExtractReq, Media, Post, Format
from pkg import ipx


logger = logging.getLogger(__name__)


async def main(params: ExtractReq):
    url = params.url.split("&list=", maxsplit=1)[0]

    # https://www.youtube.com/shorts/8no1WC4SGl0
    # https://www.youtube.com/live/4ZKLl0kU-KA?feature=share
    hostname, path_list, _ = urls.parse_url(url)
    if path_list[0] in ("shorts", "live") and len(path_list) == 2:
        url = f"https://{hostname}/watch?v={path_list[1]}"

    try:
        result = await asyncify(_sync_extract)(url)
    except Exception as e:
        if "Premieres in" in str(e):
            raise ExtractError(ExtractErrorEnum.NOT_YET_PREMIERED_ERROR)
        # 年龄限制 Sign in to confirm your age. This video may be inappropriate for some users.
        if "Sign in to confirm your age" in str(e):
            logger.exception(f"unable extract video info by yt_dlp from the given url, {url}")
            raise ExtractError(ExtractErrorEnum.GEO_RESTRICTED_ERROR)

        # 地区限制 The uploader has not made this video available in your country.
        if "The uploader has not made this video available in your country" in str(e):
            # 使用其他方式
            logger.exception(f"unable extract video info by yt_dlp from the given url, {url}")
            raise ExtractError(ExtractErrorEnum.GEO_RESTRICTED_ERROR)
        raise e

    if not result:
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

    r = Post(text=result.get("title"))

    # webp转jpg
    cover_url = result.get("thumbnail")
    if cover_url and cover_url.endswith(".webp"):
        cover_url = cover_url.replace("/vi_webp/", "/vi/").replace(".webp", ".jpg")

    def fix_dash_segments_url(f):
        if f.get("protocol") == "http_dash_segments" and f.get("fragment_base_url"):
            f["url"] = f.get("fragment_base_url")
        return f

    formats = list(map(fix_dash_segments_url, result["formats"]))

    audio_dict = {
        "webm": next(
            (f for f in formats if f.get("vcodec") == "none" and f.get("acodec") != "none" and f.get("ext") == "webm"),
            None,
        ),
        "mp4": next(
            (f for f in formats if f.get("vcodec") == "none" and f.get("acodec") != "none" and f.get("ext") == "m4a"),
            None,
        ),
    }

    def filter_(f):
        # if f.get('protocol') == 'http_dash_segments':
        #     return False
        if "av" in f["vcodec"] and "avc" not in f["vcodec"]:
            return False
        return True

    formats = list(filter(filter_, result["formats"]))

    videos = [f for f in formats if f.get("vcodec") != "none" and f.get("format_note")]

    # 排序  1、quality  2、merged(vcodec and acodec)  3、ext: mp4 > webm  4、fps  5. filesize
    def compare_format(a, b):
        if a.get("quality") != b.get("quality"):
            return a.get("quality") - b.get("quality")
        if a.get("acodec") != b.get("acodec"):
            return -1 if a.get("acodec") == "none" else 1
        if a.get("ext") != b.get("ext"):  # mp4 highest priority
            return -1 if a.get("ext") != "mp4" else 1
        if a.get("fps") != b.get("fps"):
            return a.get("fps") - b.get("fps")
        return a.get("filesize", 0) - b.get("filesize", 0)

    videos.sort(key=functools.cmp_to_key(compare_format), reverse=True)

    formats = _get_formats(videos, audio_dict, params.request_from in ("hhm", "batch_api", "ds"))
    if not formats:
        raise ValueError("no formats")

    best_quality_format = next((f for f in formats if f.separate == 0), formats[0])
    if gcr := urls.get_query_value(best_quality_format.video_url, "gcr"):
        logger.error(f"[gcr]{gcr}, {url}, {best_quality_format.video_url}")
        raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

    media = Media.video(best_quality_format.video_url, cover_url)
    media.formats = formats
    r.medias.append(media)
    return r


# 支持返回webm、mp4
def _get_formats(videos, audio_dict, av_encoding_match=False):
    format_dict = {}
    for item in videos:
        if not (matched := re.match(r"^(?P<quality>\d{3,4})p", item["format_note"])):
            continue
        quality = int(matched.group("quality"))
        if quality in format_dict:
            continue

        # 组装新数据
        f = Format(
            quality=quality,
            video_url=item["url"],
            video_size=safe_int(item["filesize"]),
            video_ext=item["ext"],
        )
        if item["acodec"] == "none":  # 音视频分离填充对应格式音频数据
            f.separate = 1
            audio = audio_dict.get(item["ext"]) if av_encoding_match else audio_dict.get("mp4", audio_dict.get("webm"))
            if audio:
                f.audio_url = audio["url"]
                f.audio_size = safe_int(audio["filesize"])
                f.audio_ext = audio["ext"]
        format_dict[quality] = f
    formats = sorted(format_dict.values(), key=lambda i: i.quality, reverse=True)
    return formats


def _sync_extract(url):
    ydl_opts: dict[str, Any] = {
        "skip_download": True,
        "quiet": True,
        "no_check_certificate": True,
    }
    if ipv6 := ipx.local_dynamic_ipv6():
        ydl_opts["source_address"] = ipv6
        logger.info(f"use ipv6: {ipv6}")
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        return ydl.extract_info(url=url, download=False)
