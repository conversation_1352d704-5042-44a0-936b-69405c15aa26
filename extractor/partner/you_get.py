import io
import json
from contextlib import redirect_stdout

from asyncer import asyncify
from knifes.media import MediaType
from you_get import common

from models import ExtractReq, Media, Post

container_type_dict = {
    "mp4": MediaType.VIDEO,
    "flv": MediaType.VIDEO,
    "m3u8": MediaType.VIDEO,
    "ts": MediaType.VIDEO,
    "3gp": MediaType.VIDEO,
    "mov": MediaType.VIDEO,
    "webm": MediaType.VIDEO,
    "asf": MediaType.VIDEO,
    "jpg": MediaType.IMAGE,
    "png": MediaType.IMAGE,
    "gif": MediaType.IMAGE,
    "mp3": MediaType.AUDIO,
    "wav": MediaType.AUDIO,
}


# https://www.bilibili.com/video/BV1rA41177He
# 优酷、爱奇艺、腾讯视频、搜狐视频
async def main(params: ExtractReq):
    result = await asyncify(_sync_extract)(params.url)
    j = json.loads(result)

    streams = j.get("streams")
    if not streams:
        raise ValueError(f"没有视频地址, {result}")

    stream = streams[0]
    if not stream.get("src"):
        raise ValueError(f"没有视频地址, {result}")

    container_type = container_type_dict.get(stream.get("container"), MediaType.VIDEO)
    src_list = stream.get("src")

    r = Post(text=j.get("title"))
    if container_type == MediaType.AUDIO:
        r.medias.append(Media.audio(src_list[0]))
    elif container_type == MediaType.IMAGE:
        r.medias.extend([Media.image(img) for img in src_list])
    else:
        r.medias.extend([Media.video(video) for video in src_list])
    return r


def _sync_extract(url):
    with redirect_stdout(io.StringIO()) as buf:
        common.any_download(url)
    return buf.getvalue()
