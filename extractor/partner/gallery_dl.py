# import logging
# from asyncer import asyncify
# from gallery_dl import job, config

# from garden.lib import url_helper
# from garden.models import ExtractParams, MediaInfo, PostResult
# from garden.services.goto_service import generate_stream_url, StreamPayload
# from garden.exceptions import ExtractError, ExtractErrorEnum
# from gardener.conf import SiteEnum, get_site_enum_by_url

# logger = logging.getLogger(__name__)


# async def main(params: ExtractParams):
#     """
#     vk: verify useragent when downloading, m3u8
#     """
#     result = await asyncify(ydl.extract_info)(url=params.url, download=False)
#     if not result:
#         raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

#     entries = result.get("entries")
#     if entries:
#         return PostResult(
#             text=result.get("title"),
#             medias=[MediaInfo(url_helper.guess_media_type(entry.get("ext")), entry["url"], entry.get("thumbnail")) for entry in entries],
#         )

#     video_url = result.get("url")
#     if not video_url and (video_urls := result.get("urls")):
#         video_url = video_urls.splitlines()[-1]

#     if not video_url:
#         raise ExtractError(ExtractErrorEnum.COMMON_URL_ERROR)

#     # if vk video, verify useragent when downloading
#     if SiteEnum.VK == get_site_enum_by_url(params.url):
#         # formats = list(filter(lambda i: i.get("protocol") == "https" and i.get("ext") == "mp4", result["formats"]))
#         # formats.sort(key=lambda i: i.get("height") or 0, reverse=True)
#         headers = dict(result["http_headers"]) if result.get("http_headers") else None
#         video_url = generate_stream_url(StreamPayload(url=video_url, headers=headers), cdn=False)
#         # video_url = generate_stream_url(StreamPayload(url=formats[0]["url"], headers=headers), cdn=False)

#     return PostResult.single_media(MediaInfo.video(video_url, result.get("thumbnail")), text=result.get("title"))


# def extract_urls(url):
#     # 设置配置,禁用下载
#     config.set((), "download", False)

#     # 创建任务
#     extractor = job.DataJob(url)

#     # 运行并收集结果
#     results = []
#     for item in extractor.items():
#         if item:
#             results.append(
#                 {
#                     "filename": item.get("filename", ""),
#                     "url": item.get("url", ""),
#                     "title": item.get("title", ""),
#                     "date": item.get("date", ""),
#                     # 可以根据需要添加更多字段
#                 }
#             )

#     return results
